#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
员工身份证信息填充脚本
根据身份证号码填充相关信息：验证、性别、出生日期、年龄、属相、星座、户籍信息等
"""

import pandas as pd
import re
from datetime import datetime, date
import warnings
warnings.filterwarnings('ignore')

# 身份证号码验证
def validate_id_card(id_card):
    """验证身份证号码是否有效"""
    if not id_card or len(id_card) != 18:
        return False
    
    # 检查前17位是否为数字
    if not id_card[:17].isdigit():
        return False
    
    # 检查最后一位是否为数字或X
    if not (id_card[17].isdigit() or id_card[17].upper() == 'X'):
        return False
    
    # 验证校验码
    weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    check_codes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
    
    sum_val = sum(int(id_card[i]) * weights[i] for i in range(17))
    check_code = check_codes[sum_val % 11]
    
    return id_card[17].upper() == check_code

# 从身份证号码提取性别
def get_gender(id_card):
    """从身份证号码提取性别"""
    if not validate_id_card(id_card):
        return ""
    gender_code = int(id_card[16])
    return "男" if gender_code % 2 == 1 else "女"

# 从身份证号码提取出生日期
def get_birth_date(id_card):
    """从身份证号码提取出生日期"""
    if not validate_id_card(id_card):
        return ""
    birth_str = id_card[6:14]
    try:
        birth_date = datetime.strptime(birth_str, '%Y%m%d').date()
        return birth_date.strftime('%Y-%m-%d')
    except:
        return ""

# 计算年龄
def calculate_age(birth_date_str):
    """根据出生日期计算年龄"""
    if not birth_date_str:
        return ""
    try:
        birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        today = date.today()
        age = today.year - birth_date.year
        if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
            age -= 1
        return age
    except:
        return ""

# 获取属相
def get_zodiac(birth_date_str):
    """根据出生日期获取属相"""
    if not birth_date_str:
        return ""
    try:
        birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        zodiac_animals = ['猴', '鸡', '狗', '猪', '鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊']
        return zodiac_animals[birth_date.year % 12]
    except:
        return ""

# 获取星座
def get_constellation(birth_date_str):
    """根据出生日期获取星座"""
    if not birth_date_str:
        return ""
    try:
        birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        month = birth_date.month
        day = birth_date.day
        
        constellations = [
            (1, 20, "摩羯座"), (2, 19, "水瓶座"), (3, 21, "双鱼座"), (4, 20, "白羊座"),
            (5, 21, "金牛座"), (6, 21, "双子座"), (7, 23, "巨蟹座"), (8, 23, "狮子座"),
            (9, 23, "处女座"), (10, 23, "天秤座"), (11, 22, "天蝎座"), (12, 22, "射手座")
        ]
        
        for i, (end_month, end_day, constellation) in enumerate(constellations):
            if month < end_month or (month == end_month and day <= end_day):
                return constellation
        return "摩羯座"  # 12月22日之后
    except:
        return ""

# 地区代码映射
def get_region_info(id_card):
    """根据身份证前6位获取地区信息"""
    if not validate_id_card(id_card):
        return "", "", ""
    
    region_code = id_card[:6]
    
    # 省份代码映射
    province_codes = {
        '11': '北京市', '12': '天津市', '13': '河北省', '14': '山西省', '15': '内蒙古自治区',
        '21': '辽宁省', '22': '吉林省', '23': '黑龙江省',
        '31': '上海市', '32': '江苏省', '33': '浙江省', '34': '安徽省', '35': '福建省', '36': '江西省', '37': '山东省',
        '41': '河南省', '42': '湖北省', '43': '湖南省', '44': '广东省', '45': '广西壮族自治区', '46': '海南省',
        '50': '重庆市', '51': '四川省', '52': '贵州省', '53': '云南省', '54': '西藏自治区',
        '61': '陕西省', '62': '甘肃省', '63': '青海省', '64': '宁夏回族自治区', '65': '新疆维吾尔自治区',
        '71': '台湾省', '81': '香港特别行政区', '82': '澳门特别行政区'
    }
    
    # 部分市级代码映射（示例）
    city_codes = {
        '3201': '南京市', '3202': '无锡市', '3203': '徐州市', '3204': '常州市', '3205': '苏州市',
        '3206': '南通市', '3207': '连云港市', '3208': '淮安市', '3209': '盐城市', '3210': '扬州市',
        '3211': '镇江市', '3212': '泰州市', '3213': '宿迁市',
        '4201': '武汉市', '4202': '黄石市', '4203': '十堰市', '4205': '宜昌市', '4206': '襄阳市',
        '4207': '鄂州市', '4208': '荆门市', '4209': '孝感市', '4210': '荆州市', '4211': '黄冈市',
        '4212': '咸宁市', '4213': '随州市', '4228': '恩施土家族苗族自治州', '4290': '仙桃市',
        '3301': '杭州市', '3302': '宁波市', '3303': '温州市', '3304': '嘉兴市', '3305': '湖州市',
        '3306': '绍兴市', '3307': '金华市', '3308': '衢州市', '3309': '舟山市', '3310': '台州市',
        '3311': '丽水市',
        '4401': '广州市', '4403': '深圳市', '4404': '珠海市', '4405': '汕头市', '4406': '佛山市',
        '4407': '江门市', '4408': '湛江市', '4409': '茂名市', '4412': '肇庆市', '4413': '惠州市',
        '4414': '梅州市', '4415': '汕尾市', '4416': '河源市', '4417': '阳江市', '4418': '清远市',
        '4419': '东莞市', '4420': '中山市', '4451': '潮州市', '4452': '揭阳市', '4453': '云浮市'
    }
    
    province_code = region_code[:2]
    city_code = region_code[:4]
    
    province = province_codes.get(province_code, "未知省份")
    city = city_codes.get(city_code, "未知城市")
    district = f"{region_code}辖区"
    
    return province, city, district

def main():
    """主函数"""
    # 读取Excel文件
    file_path = "使用Python库判断身份证真伪.xlsx"
    df = pd.read_excel(file_path, sheet_name="员工基础数据")
    
    print("开始处理员工数据...")
    
    # 处理每一行数据
    for index, row in df.iterrows():
        id_card = str(row['身份证号']).strip()
        
        # 验证身份证
        is_valid = validate_id_card(id_card)
        df.at[index, '身份证号验证'] = "有效" if is_valid else "无效"
        
        if is_valid:
            # 提取性别
            df.at[index, '性别'] = get_gender(id_card)
            
            # 提取出生日期
            birth_date = get_birth_date(id_card)
            df.at[index, '出生日'] = birth_date
            
            # 计算年龄
            df.at[index, '年龄(周岁)'] = calculate_age(birth_date)
            
            # 获取属相
            df.at[index, '属相'] = get_zodiac(birth_date)
            
            # 获取星座
            df.at[index, '星座'] = get_constellation(birth_date)
            
            # 获取地区信息
            province, city, district = get_region_info(id_card)
            df.at[index, '户籍省份'] = province
            df.at[index, '户籍市'] = city
            df.at[index, '所属辖区'] = district
        
        print(f"处理完成: {row['姓名']} - {id_card}")
    
    # 保存结果
    output_file = "员工基础数据_完整版.xlsx"
    df.to_excel(output_file, sheet_name="员工基础数据", index=False)
    print(f"\n数据处理完成！结果已保存到: {output_file}")
    
    # 显示统计信息
    valid_count = len(df[df['身份证号验证'] == '有效'])
    invalid_count = len(df[df['身份证号验证'] == '无效'])
    print(f"\n统计信息:")
    print(f"有效身份证: {valid_count} 个")
    print(f"无效身份证: {invalid_count} 个")
    print(f"总计: {len(df)} 个")

if __name__ == "__main__":
    main()
